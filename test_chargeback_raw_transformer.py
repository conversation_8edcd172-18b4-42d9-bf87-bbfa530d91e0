#!/usr/bin/env python3

import csv
import tempfile
import os
import sys
import logging
from typing import List, Dict

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# Add the project root to Python path
sys.path.append('/Users/<USER>/phonepe/lucy-mcp-airflow-dag')

try:
    from mcp_reports.dispute_services.transformers.chargeback_raw_data_transformer import (
        process, parse_raw_dispute_data, calculate_metrics_from_disputes, 
        generate_loss_breakdown_with_ids, DisputeRecord, ChargebackMetrics
    )
    DEPENDENCIES_AVAILABLE = True
except ImportError as e:
    print(f"Warning: Could not import transformer dependencies: {e}")
    print("Running in standalone mode with mock functions...")
    DEPENDENCIES_AVAILABLE = False


def create_comprehensive_test_data():
    """Create comprehensive test data covering all scenarios"""
    test_data = [
        # Headers
        ["dispute_workflow_id", "transaction_id", "current_state", "dispute_type", "dispute_stage", 
         "disputed_amount", "accepted_amount", "raised_at", "eventdata_disputeworkflowid", 
         "eventdata_signaltype", "eventdata_signalamount", "rank_num"],
        
        # UPI Chargebacks (dispute_type = 0)
        # Case 1: UPI - Recovery initiated (state 23, stage 0, no signal)
        ["DW001", "TXN001", "23", "0", "0", "10000", "8000", "2025-04-01", "DW001", "", "", "1"],
        
        # Case 2: UPI - Wrongly marked chargeback (state 4, stage 0, no signal)
        ["DW002", "TXN002", "4", "0", "0", "15000", "0", "2025-04-02", "DW002", "", "", "1"],
        
        # Case 3: UPI - RGCS accepted and NPCI accepted (state 2, stage 0, no signal)
        ["DW003", "TXN003", "2", "0", "0", "5000", "0", "2025-04-03", "DW003", "", "", "1"],
        
        # Case 4: UPI - Finance absorption (state 14)
        ["DW004", "TXN004", "14", "0", "0", "25000", "0", "2025-04-04", "DW004", "", "", "1"],
        
        # Case 5: UPI - Stage 1 with DEBIT signal and recovery (state 23)
        ["DW005", "TXN005", "23", "0", "1", "12000", "10000", "2025-04-05", "DW005", "DEBIT", "12000", "1"],
        
        # Case 6: UPI - Stage 1 with DEBIT signal, wrongly marked (state 4)
        ["DW006", "TXN006", "4", "0", "1", "8000", "0", "2025-04-06", "DW006", "DEBIT", "8000", "1"],
        
        # PG Chargebacks (dispute_type = 1)
        # Case 7: PG - Debit signal processed pending on ops (state 28)
        ["DW007", "TXN007", "28", "1", "0", "20000", "0", "2025-04-07", "DW007", "DEBIT", "20000", "1"],
        
        # Case 8: PG - Debit signal not processed (state 31)
        ["DW008", "TXN008", "31", "1", "0", "12000", "0", "2025-04-08", "DW008", "DEBIT", "12000", "1"],
        
        # Case 9: PG - Recovery initiated (state 23)
        ["DW009", "TXN009", "23", "1", "0", "18000", "15000", "2025-04-09", "DW009", "DEBIT", "18000", "1"],
        
        # Case 10: PG - Wrongly marked (state 30, stage 0)
        ["DW010", "TXN010", "30", "1", "0", "9000", "0", "2025-04-10", "DW010", "DEBIT", "9000", "1"],
        
        # Case 11: PG - Wrongly marked (state 30, stage 1)
        ["DW011", "TXN011", "30", "1", "1", "7000", "0", "2025-04-11", "DW011", "DEBIT", "7000", "1"],
        
        # EDC Chargebacks (dispute_type = 5)
        # Case 12: EDC - Pending on ops to move (state 6)
        ["DW012", "TXN012", "6", "5", "0", "8000", "0", "2025-04-12", "DW012", "DEBIT", "8000", "1"],
        
        # Case 13: EDC - Pending on ops to move (state 11)
        ["DW013", "TXN013", "11", "5", "0", "6000", "0", "2025-04-13", "DW013", "DEBIT", "6000", "1"],
        
        # Case 14: EDC - Debit signal processed pending on ops (state 28)
        ["DW014", "TXN014", "28", "5", "0", "11000", "0", "2025-04-14", "DW014", "DEBIT", "11000", "1"],
        
        # Case 15: EDC - Recovery initiated (state 23)
        ["DW015", "TXN015", "23", "5", "0", "13000", "11000", "2025-04-15", "DW015", "DEBIT", "13000", "1"],
        
        # Additional edge cases
        # Case 16: Finance absorption (state 16)
        ["DW016", "TXN016", "16", "0", "0", "3000", "0", "2025-04-16", "DW016", "", "", "1"],
        
        # Case 17: Finance absorption (state 17)
        ["DW017", "TXN017", "17", "1", "0", "4000", "0", "2025-04-17", "DW017", "DEBIT", "4000", "1"],
        
        # Case 18: No loss case (fully recovered)
        ["DW018", "TXN018", "23", "0", "0", "5000", "5000", "2025-04-18", "DW018", "", "", "1"],
    ]
    
    return test_data


def create_test_tsv_file(test_data):
    """Create a temporary TSV file with test data"""
    with tempfile.NamedTemporaryFile(mode='w', suffix='.tsv', delete=False) as f:
        writer = csv.writer(f, delimiter='\t')
        writer.writerows(test_data)
        return f.name


def verify_expected_results(csv_file_path):
    """Verify the generated report contains expected results"""
    print("\n" + "="*80)
    print("VERIFYING GENERATED REPORT")
    print("="*80)
    
    with open(csv_file_path, 'r', encoding='utf-8') as f:
        content = f.read()
        print(content)
    
    # Parse the CSV to verify specific values
    lines = content.strip().split('\n')
    
    # Find main report section
    main_report_found = False
    breakdown_found = False
    loss_detail_found = False
    
    expected_checks = {
        'total_chargeback_found': False,
        'amount_debited_found': False,
        'recovery_initiated_found': False,
        'recovery_pending_found': False,
        'breakdown_header_found': False,
        'loss_detail_header_found': False
    }
    
    for line in lines:
        if 'Total Chargeback Registered on Stratos' in line:
            expected_checks['total_chargeback_found'] = True
            print(f"✓ Found total chargeback line: {line}")
        
        elif 'Amount debited by NPCI/PGs/Axis Bank' in line:
            expected_checks['amount_debited_found'] = True
            print(f"✓ Found amount debited line: {line}")
        
        elif 'Merchant recovery initiated from Stratos' in line:
            expected_checks['recovery_initiated_found'] = True
            print(f"✓ Found recovery initiated line: {line}")
        
        elif 'Merchant recovery yet to be initiated' in line:
            expected_checks['recovery_pending_found'] = True
            print(f"✓ Found recovery pending line: {line}")
        
        elif "Reasons for 'Merchant recovery yet to be initiated'" in line:
            expected_checks['breakdown_header_found'] = True
            print(f"✓ Found breakdown header: {line}")
        
        elif 'Dispute Workflow ID' in line and 'Transaction ID' in line:
            expected_checks['loss_detail_header_found'] = True
            print(f"✓ Found loss detail header: {line}")
    
    # Verify all expected sections are present
    all_checks_passed = all(expected_checks.values())
    
    print(f"\n{'='*50}")
    print("VERIFICATION RESULTS:")
    print(f"{'='*50}")
    
    for check, passed in expected_checks.items():
        status = "✓ PASS" if passed else "✗ FAIL"
        print(f"{check}: {status}")
    
    print(f"\nOverall Result: {'✓ ALL TESTS PASSED' if all_checks_passed else '✗ SOME TESTS FAILED'}")
    
    return all_checks_passed


def calculate_expected_metrics():
    """Calculate expected metrics manually for verification"""
    print("\n" + "="*80)
    print("CALCULATING EXPECTED METRICS")
    print("="*80)
    
    # Based on our test data, calculate expected values
    expected_metrics = {
        'UPI': {
            'disputed_amount': 10000 + 15000 + 5000 + 25000 + 12000 + 8000 + 3000 + 5000,  # 83000
            'amount_debited': 10000 + 15000 + 5000 + 25000 + 12000 + 8000 + 3000 + 5000,   # 83000
            'recovery_initiated': 8000 + 10000 + 5000,  # 23000
            'loss': 83000 - 23000  # 60000
        },
        'PG': {
            'disputed_amount': 20000 + 12000 + 18000 + 9000 + 7000 + 4000,  # 70000
            'amount_debited': 20000 + 12000 + 18000 + 9000 + 7000 + 4000,   # 70000
            'recovery_initiated': 15000,  # 15000
            'loss': 70000 - 15000  # 55000
        },
        'EDC': {
            'disputed_amount': 8000 + 6000 + 11000 + 13000,  # 38000
            'amount_debited': 8000 + 6000 + 11000 + 13000,   # 38000
            'recovery_initiated': 11000,  # 11000
            'loss': 38000 - 11000  # 27000
        }
    }
    
    print("Expected Metrics:")
    for channel, metrics in expected_metrics.items():
        print(f"\n{channel}:")
        for metric, value in metrics.items():
            print(f"  {metric}: {value}")
    
    total_disputed = sum(m['disputed_amount'] for m in expected_metrics.values())
    total_debited = sum(m['amount_debited'] for m in expected_metrics.values())
    total_recovery = sum(m['recovery_initiated'] for m in expected_metrics.values())
    total_loss = sum(m['loss'] for m in expected_metrics.values())
    
    print(f"\nTOTALS:")
    print(f"  disputed_amount: {total_disputed}")
    print(f"  amount_debited: {total_debited}")
    print(f"  recovery_initiated: {total_recovery}")
    print(f"  loss: {total_loss}")
    
    return expected_metrics


def test_transformer():
    """Main test function"""
    print("TESTING CHARGEBACK RAW DATA TRANSFORMER")
    print("="*60)
    
    if not DEPENDENCIES_AVAILABLE:
        print("❌ Dependencies not available. Cannot run full test.")
        return False
    
    try:
        # Calculate expected results
        expected_metrics = calculate_expected_metrics()
        
        # Create test data
        print("\n📝 Creating test data...")
        test_data = create_comprehensive_test_data()
        tsv_file = create_test_tsv_file(test_data)
        print(f"✓ Test TSV file created: {tsv_file}")
        
        # Create output file path
        csv_file = tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False).name
        print(f"✓ Output CSV file: {csv_file}")
        
        # Process the data
        print(f"\n🔄 Processing {tsv_file} -> {csv_file}")
        process(tsv_file, csv_file)
        print("✓ Processing completed successfully")
        
        # Verify results
        verification_passed = verify_expected_results(csv_file)
        
        print(f"\n{'='*60}")
        print(f"TEST RESULT: {'✅ SUCCESS' if verification_passed else '❌ FAILED'}")
        print(f"{'='*60}")
        
        # Keep output file for manual inspection
        print(f"\n📄 Generated report saved at: {csv_file}")
        print("You can manually inspect this file to verify the output format.")
        
        return verification_passed
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # Clean up TSV file
        if 'tsv_file' in locals() and os.path.exists(tsv_file):
            os.unlink(tsv_file)
            print(f"🧹 Cleaned up temporary TSV file: {tsv_file}")


if __name__ == "__main__":
    success = test_transformer()
    sys.exit(0 if success else 1)
