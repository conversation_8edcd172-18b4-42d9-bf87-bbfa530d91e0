#!/usr/bin/env python3

import csv
import os
import sys
import logging
import argparse
from pathlib import Path
from typing import Optional

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# Add the project root to Python path
sys.path.append('/Users/<USER>/phonepe/lucy-mcp-airflow-dag')

try:
    from mcp_reports.dispute_services.transformers.chargeback_raw_data_transformer import process
    DEPENDENCIES_AVAILABLE = True
except ImportError as e:
    print(f"Error: Could not import transformer dependencies: {e}")
    print("Please ensure the project dependencies are properly installed.")
    DEPENDENCIES_AVAILABLE = False


def validate_input_file(file_path: str) -> bool:
    """Validate that the input file exists and has the correct format"""
    if not os.path.exists(file_path):
        print(f"❌ Error: Input file does not exist: {file_path}")
        return False
    
    # Check if it's a TSV file
    if not file_path.lower().endswith(('.tsv', '.csv')):
        print(f"⚠️  Warning: Input file should be a TSV or CSV file: {file_path}")
    
    # Try to read the first few lines to validate format
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            # Determine delimiter
            first_line = f.readline()
            if '\t' in first_line:
                delimiter = '\t'
            else:
                delimiter = ','
            
            f.seek(0)  # Reset to beginning
            reader = csv.reader(f, delimiter=delimiter)
            headers = next(reader)
            
            # Check if we have the expected columns
            expected_columns = [
                'dispute_workflow_id', 'transaction_id', 'current_state', 
                'dispute_type', 'dispute_stage', 'disputed_amount', 
                'accepted_amount', 'raised_at'
            ]
            
            # Check if we have at least the minimum required columns
            if len(headers) < 8:
                print(f"⚠️  Warning: Input file has only {len(headers)} columns, expected at least 8")
                print(f"Headers found: {headers}")
                return True  # Still allow processing, transformer will handle it
            
            print(f"✓ Input file validation passed")
            file_format = 'TSV' if delimiter == '\t' else 'CSV'
            print(f"  - File format: {file_format}")
            print(f"  - Columns found: {len(headers)}")
            print(f"  - Headers: {headers[:8]}...")  # Show first 8 headers
            
            return True
            
    except Exception as e:
        print(f"❌ Error validating input file: {e}")
        return False


def generate_output_filename(input_file_path: str, output_file_path: Optional[str] = None) -> str:
    """Generate output filename if not provided"""
    if output_file_path:
        return output_file_path
    
    # Generate output filename based on input filename
    input_path = Path(input_file_path)
    output_filename = f"{input_path.stem}_chargeback_report.csv"
    output_path = input_path.parent / output_filename
    
    return str(output_path)


def display_report_summary(output_file_path: str):
    """Display a summary of the generated report"""
    print("\n" + "="*80)
    print("GENERATED REPORT SUMMARY")
    print("="*80)
    
    try:
        with open(output_file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        print(f"📄 Report file: {output_file_path}")
        print(f"📊 Total lines: {len(lines)}")
        
        # Find key sections
        sections_found = {
            'Main Report': False,
            'Breakdown Report': False,
            'Loss Details': False
        }
        
        for i, line in enumerate(lines):
            if 'Total Chargeback Registered on Stratos' in line:
                sections_found['Main Report'] = True
                print(f"✓ Main Report section found at line {i+1}")
            elif "Reasons for 'Merchant recovery yet to be initiated'" in line:
                sections_found['Breakdown Report'] = True
                print(f"✓ Breakdown Report section found at line {i+1}")
            elif 'Dispute Workflow ID' in line and 'Transaction ID' in line:
                sections_found['Loss Details'] = True
                print(f"✓ Loss Details section found at line {i+1}")
        
        print(f"\n📋 Report Sections:")
        for section, found in sections_found.items():
            status = "✓ Present" if found else "✗ Missing"
            print(f"  {section}: {status}")
        
        # Show first few lines of actual data
        print(f"\n📝 First 10 lines of report:")
        for i, line in enumerate(lines[:10]):
            print(f"  {i+1:2d}: {line.rstrip()}")

        if len(lines) > 10:
            print(f"  ... ({len(lines) - 10} more lines)")
            
    except Exception as e:
        print(f"❌ Error reading report summary: {e}")


def test_chargeback_transformer_with_file(input_file_path: str, output_file_path: Optional[str] = None) -> bool:
    """
    Test the chargeback transformer with a specific input file
    
    Args:
        input_file_path: Path to the input TSV/CSV file containing dispute data
        output_file_path: Optional path for the output CSV file. If not provided, 
                         will be generated based on input filename
    
    Returns:
        bool: True if transformation was successful, False otherwise
    """
    print("CHARGEBACK RAW DATA TRANSFORMER - FILE INPUT TEST")
    print("="*60)
    
    if not DEPENDENCIES_AVAILABLE:
        print("❌ Dependencies not available. Cannot run transformer.")
        return False
    
    try:
        # Validate input file
        print(f"📂 Input file: {input_file_path}")
        if not validate_input_file(input_file_path):
            return False
        
        # Generate output filename
        output_file_path = generate_output_filename(input_file_path, output_file_path)
        print(f"📄 Output file: {output_file_path}")
        
        # Ensure output directory exists
        output_dir = os.path.dirname(output_file_path)
        if output_dir and not os.path.exists(output_dir):
            os.makedirs(output_dir)
            print(f"✓ Created output directory: {output_dir}")
        
        # Process the data
        print(f"\n🔄 Processing transformation...")
        print(f"   Input:  {input_file_path}")
        print(f"   Output: {output_file_path}")
        
        process(input_file_path, output_file_path)
        
        print("✓ Transformation completed successfully!")
        
        # Verify output file was created
        if not os.path.exists(output_file_path):
            print(f"❌ Error: Output file was not created: {output_file_path}")
            return False
        
        # Display report summary
        display_report_summary(output_file_path)
        
        print(f"\n{'='*60}")
        print("✅ TEST COMPLETED SUCCESSFULLY")
        print(f"{'='*60}")
        print(f"📄 Generated report: {output_file_path}")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Main function with command line argument parsing"""
    parser = argparse.ArgumentParser(
        description="Test chargeback raw data transformer with file input",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python test_chargeback_transformer_file_input.py input_data.tsv
  python test_chargeback_transformer_file_input.py input_data.csv -o output_report.csv
  python test_chargeback_transformer_file_input.py /path/to/dispute_data.tsv -o /path/to/reports/chargeback_analysis.csv
        """
    )
    
    parser.add_argument(
        'input_file',
        help='Path to the input TSV or CSV file containing dispute data'
    )
    
    parser.add_argument(
        '-o', '--output',
        help='Path for the output CSV file (optional, will be auto-generated if not provided)'
    )
    
    parser.add_argument(
        '-v', '--verbose',
        action='store_true',
        help='Enable verbose logging'
    )
    
    args = parser.parse_args()
    
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # Run the test
    success = test_chargeback_transformer_with_file(args.input_file, args.output)
    
    if success:
        print("\n🎉 Transformation completed successfully!")
        print("You can now analyze the generated report.")
    else:
        print("\n💥 Transformation failed!")
        print("Please check the error messages above and ensure your input file is properly formatted.")
    
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
