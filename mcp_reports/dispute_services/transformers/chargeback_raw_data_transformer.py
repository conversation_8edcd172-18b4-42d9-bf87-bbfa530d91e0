import csv
import logging
from dataclasses import dataclass
from enum import Enum
from typing import Dict, List, Tuple, Optional
from mcp_reports.dispute_services.utils.common import remove_file_pattern, create_dir_for_file, append_to_file_top, \
    append_row_to_csv, write_empty_row, convert_tsv_to_csv, create_empty_csv, read_file_content, \
    create_temporary_csv, _parse_float_value, append_to_file_top_safely, convert_paise_to_rs


class MetricKeys(Enum):
    DISPUTED_AMOUNT = 'disputed_amount'
    AMOUNT_DEBITED = 'amount_debited'
    AMOUNT_RECOVERED = 'amount_recovered'
    LOSS = 'loss'
    WRONGLY_MARKED_CHARGEBACK_AMOUNT = 'wrongly_marked_chargeback_amount'
    RGCS_ACCEPTED_AND_NPCI_ACCEPTED = 'rgcs_accepted_and_npci_accepted'
    DEBIT_SIGNAL_PROCESSED_PENDING_ON_OPS = 'debit_signal_processed_pending_on_ops'
    PENDING_ON_OPS_TO_MOVE = 'pending_on_ops_to_move'
    ABSORBED_REQUESTED_BY_OPS = 'absorbed_requested_by_ops'
    DEBIT_SIGNAL_NOT_PROCESSED = 'debit_signal_not_processed'
    CHARGEBACK_ABSORBED = 'chargeback_absorbed'
    MONEY_RECOVERY_REVERSED = 'money_recovery_reversed'


class ChannelKeys(Enum):
    UPI_CHARGEBACK = 'upi_chargeback'
    PG_CHARGEBACK = 'pg_chargeback'
    EDC_CHARGEBACK = 'edc_chargeback'
    TOTAL = 'total'


@dataclass
class ChargebackMetrics:
    upi_chargeback: float = 0
    pg_chargeback: float = 0
    edc_chargeback: float = 0
    total: float = 0

    def to_string_dict(self) -> Dict[str, str]:
        return {
            ChannelKeys.UPI_CHARGEBACK.value: str(int(self.upi_chargeback)),
            ChannelKeys.PG_CHARGEBACK.value: str(int(self.pg_chargeback)),
            ChannelKeys.EDC_CHARGEBACK.value: str(int(self.edc_chargeback)),
            ChannelKeys.TOTAL.value: str(int(self.total))
        }


@dataclass
class DisputeRecord:
    dispute_workflow_id: str
    transaction_id: str
    current_state: int
    dispute_type: int
    dispute_stage: int
    disputed_amount: float
    accepted_amount: float
    raised_at: str
    signal_type: str = None
    signal_amount: float = 0


@dataclass
class ReportHeaders:
    MAIN_REPORT = ["Data (All figures in Rs)", "UPI", "PG", "EDC", "Total"]
    BREAKDOWN_REPORT = [
        "Reasons for 'Merchant recovery yet to be initiated'",
        "UPI", "PG", "EDC", "Total",
        "Description", "Owner", "Expected Actions"
    ]


def parse_raw_dispute_data(filename) -> List[DisputeRecord]:
    """Parse raw dispute data from CSV file into DisputeRecord objects"""
    try:
        disputes = []
        with open(filename, 'r', newline='', encoding='utf-8') as csvfile:
            reader = csv.reader(csvfile)
            headers = next(reader)  # Skip header row
            
            for row_num, row in enumerate(reader, 2):

                try:
                    dispute = DisputeRecord(
                        dispute_workflow_id=row[0].strip(),
                        transaction_id=row[1].strip(),
                        current_state=int(row[2]),
                        dispute_type=int(row[3]),
                        dispute_stage=int(row[4]),
                        disputed_amount=float(row[5]) if row[5].isnumeric() else 0.0,
                        accepted_amount=float(row[6]) if row[6].isnumeric() else 0.0,
                        raised_at=row[7].strip(),
                        signal_type=row[8].strip() if row[8] in ['CREDIT', 'DEBIT'] else None,
                        signal_amount=float(row[9]) if row[9].isnumeric() else 0.0
                    )
                    disputes.append(dispute)
                except (ValueError, IndexError) as e:
                    logging.warning(f"Error parsing row {row_num}: {e}")
                    continue
        
        logging.info(f"Parsed {len(disputes)} dispute records from {filename}")
        return disputes
    
    except FileNotFoundError:
        error_msg = f"CSV file not found: {filename}"
        logging.error(error_msg)
        raise FileNotFoundError(error_msg)
    except Exception as e:
        error_msg = f"Failed to parse raw dispute data from {filename}: {e}"
        logging.error(error_msg)
        raise RuntimeError(error_msg)


def get_dispute_type_string(dispute_type: int) -> Optional[str]:
    """Convert dispute type integer to string"""
    if dispute_type == 0:
        return 'UPI_CHARGEBACK'
    elif dispute_type == 1:
        return 'PG_CHARGEBACK'
    elif dispute_type == 5:
        return 'EDC_CHARGEBACK'


def _add_to_metrics(metrics: ChargebackMetrics, dispute_type: str, amount: float):
    """Add amount to appropriate metric based on dispute type"""
    # Convert amount from paise to rupees
    amount_in_rupees = amount / 100

    if dispute_type == 'UPI_CHARGEBACK':
        metrics.upi_chargeback += amount_in_rupees
    elif dispute_type == 'PG_CHARGEBACK':
        metrics.pg_chargeback += amount_in_rupees
    elif dispute_type == 'EDC_CHARGEBACK':
        metrics.edc_chargeback += amount_in_rupees
    metrics.total += amount_in_rupees


def _calculate_amount_debited(dispute: DisputeRecord) -> float:
    """Calculate amount debited based on dispute conditions"""
    if (dispute.dispute_type == 0 and dispute.dispute_stage == 0 and 
        (dispute.signal_type != 'CREDIT' or dispute.signal_type is None)):
        return dispute.disputed_amount
    elif (dispute.dispute_type == 0 and dispute.dispute_stage == 1 and 
          dispute.signal_type == 'DEBIT'):
        return dispute.signal_amount
    elif dispute.dispute_type != 0 and dispute.signal_type == 'DEBIT':
        return dispute.signal_amount
    else:
        return 0


def _calculate_recovery_initiated(dispute: DisputeRecord) -> float:
    """Calculate recovery initiated based on dispute conditions"""
    if (dispute.dispute_type == 0 and dispute.dispute_stage == 0 and 
        dispute.signal_type is None and dispute.current_state == 23):
        return dispute.accepted_amount
    elif (dispute.dispute_type == 0 and dispute.dispute_stage == 1 and 
          dispute.signal_type == 'DEBIT' and dispute.current_state == 23):
        return dispute.accepted_amount
    elif (dispute.dispute_type in (1, 5) and dispute.signal_type == 'DEBIT' and 
          dispute.current_state == 23):
        return dispute.accepted_amount
    else:
        return 0


def _calculate_wrongly_marked_chargeback(dispute: DisputeRecord) -> float:
    """Calculate wrongly marked chargeback amount"""
    if (dispute.dispute_type == 0 and dispute.dispute_stage == 0 and 
        dispute.current_state in [3, 4, 9] and dispute.signal_type is None):
        return dispute.disputed_amount
    elif (dispute.dispute_type == 0 and dispute.dispute_stage == 1 and 
          dispute.current_state in [3, 4] and dispute.signal_type == 'DEBIT'):
        return dispute.disputed_amount
    elif (dispute.dispute_type == 1 and dispute.dispute_stage in (0, 1) and 
          dispute.current_state in  [3, 30] and dispute.signal_type == 'DEBIT'):
        return dispute.disputed_amount
    elif (dispute.dispute_type == 5 and dispute.dispute_stage in (0, 1) and
          dispute.current_state in  [3, 48] and dispute.signal_type == 'DEBIT'):
        return dispute.disputed_amount
    else:
        return 0


def _calculate_absorbed_requested_by_ops(dispute: DisputeRecord) -> float:
    """Calculate absorbed or requested by ops amount"""
    if dispute.current_state in [14]:
        return dispute.disputed_amount
    else:
        return 0


def _calculate_rgcs_accepted_and_npci_accepted(dispute: DisputeRecord) -> float:
    """Calculate RGCS accepted and NPCI accepted amount"""
    if (dispute.dispute_type == 0 and dispute.dispute_stage == 0 and 
        dispute.current_state in [1, 2] and dispute.signal_type is None):
        return dispute.disputed_amount
    if (dispute.dispute_type == 0 and dispute.dispute_stage == 1 and
        dispute.current_state in [1, 2] and dispute.signal_type is 'DEBIT'):
        return dispute.disputed_amount
    else:
        return 0


def _calculate_debit_signal_processed_pending_on_ops(dispute: DisputeRecord) -> float:
    """Calculate debit signal processed pending on ops amount"""
    if (dispute.dispute_type in (1, 5) and dispute.current_state == 28 and 
        dispute.signal_type == 'DEBIT'):
        return dispute.disputed_amount
    else:
        return 0


def _calculate_debit_signal_not_processed(dispute: DisputeRecord) -> float:
    """Calculate debit signal not processed amount"""
    if (dispute.dispute_type == 0 and dispute.dispute_stage == 1 and dispute.current_state == 13 and
        dispute.signal_type == 'DEBIT'):
        return dispute.disputed_amount
    if (dispute.dispute_type == 1 and dispute.current_state in [31, 32] and
        dispute.signal_type == 'DEBIT'):
        return dispute.disputed_amount
    if (dispute.dispute_type == 5 and dispute.current_state in [49] and
        dispute.signal_type == 'DEBIT'):
        return dispute.disputed_amount
    else:
        return 0


def _calculate_pending_on_ops_to_move(dispute: DisputeRecord) -> float:
    """Calculate pending on ops to move amount"""
    if (dispute.dispute_type == 0 and dispute.dispute_stage == 0 and dispute.signal_type is None
            and dispute.current_state in [6, 7, 8, 11, 12, 38]):
        return dispute.disputed_amount
    if (dispute.dispute_type == 0 and dispute.dispute_stage == 1 and dispute.signal_type == 'DEBIT'
            and dispute.current_state in [6, 7, 8, 11, 12, 38]):
        return dispute.disputed_amount
    if (dispute.dispute_type == 1 and dispute.current_state in [6, 7, 8, 11, 12, 38] and
        dispute.signal_type == 'DEBIT'):
        return dispute.disputed_amount
    if (dispute.dispute_type == 5 and dispute.current_state in [6, 7, 8, 11, 12, 38] and
        dispute.signal_type == 'DEBIT'):
        return dispute.disputed_amount
    else:
        return 0

def _calculate_chargeback_absorbed(dispute: DisputeRecord) -> float:
    """Calculate chargeback absorbed amount"""
    if dispute.current_state in [16, 17]:
        return dispute.accepted_amount
    else:
        return 0

def _calculate_money_recovery_reversed(dispute: DisputeRecord) -> float:
    """Calculate money recovery reversed amount"""
    if dispute.current_state == 27:
        return dispute.accepted_amount
    else:
        return 0

def calculate_metrics_from_disputes(disputes: List[DisputeRecord]) -> Dict[str, ChargebackMetrics]:
    """Calculate all metrics from raw dispute data"""
    metrics = {
        MetricKeys.DISPUTED_AMOUNT.value: ChargebackMetrics(),
        MetricKeys.AMOUNT_DEBITED.value: ChargebackMetrics(),
        MetricKeys.AMOUNT_RECOVERED.value: ChargebackMetrics(),
        MetricKeys.LOSS.value: ChargebackMetrics(),
        MetricKeys.WRONGLY_MARKED_CHARGEBACK_AMOUNT.value: ChargebackMetrics(),
        MetricKeys.RGCS_ACCEPTED_AND_NPCI_ACCEPTED.value: ChargebackMetrics(),
        MetricKeys.DEBIT_SIGNAL_PROCESSED_PENDING_ON_OPS.value: ChargebackMetrics(),
        MetricKeys.PENDING_ON_OPS_TO_MOVE.value: ChargebackMetrics(),
        MetricKeys.ABSORBED_REQUESTED_BY_OPS.value: ChargebackMetrics(),
        MetricKeys.DEBIT_SIGNAL_NOT_PROCESSED.value: ChargebackMetrics(),
        MetricKeys.CHARGEBACK_ABSORBED.value: ChargebackMetrics(),
        MetricKeys.MONEY_RECOVERY_REVERSED.value: ChargebackMetrics()
    }
    
    for dispute in disputes:
        dispute_type_str = get_dispute_type_string(dispute.dispute_type)
        
        # Calculate disputed amount
        _add_to_metrics(metrics[MetricKeys.DISPUTED_AMOUNT.value], dispute_type_str, dispute.disputed_amount)
        
        # Calculate amount debited
        amount_debited = _calculate_amount_debited(dispute)
        _add_to_metrics(metrics[MetricKeys.AMOUNT_DEBITED.value], dispute_type_str, amount_debited)
        
        # Calculate recovery initiated
        recovery_initiated = _calculate_recovery_initiated(dispute)
        _add_to_metrics(metrics[MetricKeys.AMOUNT_RECOVERED.value], dispute_type_str, recovery_initiated)
        
        # Calculate loss (amount_debited - recovery_initiated)
        loss = amount_debited - recovery_initiated
        _add_to_metrics(metrics[MetricKeys.LOSS.value], dispute_type_str, loss)
        
        # Calculate other specific metrics
        wrongly_marked = _calculate_wrongly_marked_chargeback(dispute)
        _add_to_metrics(metrics[MetricKeys.WRONGLY_MARKED_CHARGEBACK_AMOUNT.value], dispute_type_str, wrongly_marked)
        
        rgcs_npci = _calculate_rgcs_accepted_and_npci_accepted(dispute)
        _add_to_metrics(metrics[MetricKeys.RGCS_ACCEPTED_AND_NPCI_ACCEPTED.value], dispute_type_str, rgcs_npci)
        
        debit_pending = _calculate_debit_signal_processed_pending_on_ops(dispute)
        _add_to_metrics(metrics[MetricKeys.DEBIT_SIGNAL_PROCESSED_PENDING_ON_OPS.value], dispute_type_str, debit_pending)
        
        pending_ops = _calculate_pending_on_ops_to_move(dispute)
        _add_to_metrics(metrics[MetricKeys.PENDING_ON_OPS_TO_MOVE.value], dispute_type_str, pending_ops)
        
        absorbed_requested = _calculate_absorbed_requested_by_ops(dispute)
        _add_to_metrics(metrics[MetricKeys.ABSORBED_REQUESTED_BY_OPS.value], dispute_type_str, absorbed_requested)
        
        debit_not_processed = _calculate_debit_signal_not_processed(dispute)
        _add_to_metrics(metrics[MetricKeys.DEBIT_SIGNAL_NOT_PROCESSED.value], dispute_type_str, debit_not_processed)

        absorbed = _calculate_chargeback_absorbed(dispute)
        _add_to_metrics(metrics[MetricKeys.CHARGEBACK_ABSORBED.value], dispute_type_str, absorbed)

        money_recovery_reversed = _calculate_money_recovery_reversed(dispute)
        _add_to_metrics(metrics[MetricKeys.MONEY_RECOVERY_REVERSED.value], dispute_type_str, money_recovery_reversed)

    return metrics


class MainReportRowGenerator:

    @staticmethod
    def _create_empty_row(headers) -> Dict[str, str]:
        return {header: "" for header in headers}

    @staticmethod
    def _populate_metrics_data(row, headers, metrics) -> None:
        if metrics and len(headers) >= 5:
            metrics_dict = metrics.to_string_dict()
            row[headers[1]] = metrics_dict[ChannelKeys.UPI_CHARGEBACK.value]
            row[headers[2]] = metrics_dict[ChannelKeys.PG_CHARGEBACK.value]
            row[headers[3]] = metrics_dict[ChannelKeys.EDC_CHARGEBACK.value]
            row[headers[4]] = metrics_dict[ChannelKeys.TOTAL.value]

    @classmethod
    def get_total_chargeback_registered_row(
        cls, data_dictionary, headers) -> Dict[str, str]:
        row = cls._create_empty_row(headers)
        row[headers[0]] = "Total Chargeback Registered on Stratos"

        metrics = data_dictionary.get(MetricKeys.DISPUTED_AMOUNT.value)
        cls._populate_metrics_data(row, headers, metrics)
        return row

    @classmethod
    def get_amount_debited_row(cls, data_dictionary, headers) -> Dict[str, str]:
        row = cls._create_empty_row(headers)
        row[headers[0]] = "Amount debited by NPCI/PGs/Axis Bank\n(i.e. Chargeback Accepted by PhonePe)"

        metrics = data_dictionary.get(MetricKeys.AMOUNT_DEBITED.value)
        cls._populate_metrics_data(row, headers, metrics)
        return row

    @classmethod
    def get_merchant_recovery_initiated_row(cls, data_dictionary, headers) -> Dict[str, str]:
        row = cls._create_empty_row(headers)
        row[headers[0]] = "Merchant recovery initiated from Stratos for Accepted Chargebacks"

        metrics = data_dictionary.get(MetricKeys.AMOUNT_RECOVERED.value)
        cls._populate_metrics_data(row, headers, metrics)
        return row

    @classmethod
    def get_merchant_recovery_pending_row(cls, data_dictionary, headers) -> Dict[str, str]:
        row = cls._create_empty_row(headers)
        row[headers[0]] = "Merchant recovery yet to be initiated for Accepted Chargebacks*"

        metrics = data_dictionary.get(MetricKeys.LOSS.value)
        cls._populate_metrics_data(row, headers, metrics)
        return row


@dataclass
class LossBreakdownRowConfig:
    title: str
    description: str
    owner: str
    expected_actions: str
    metric_key: str


class LossBreakdownReportRowGenerator:
    LOSS_BREAKDOWN_CONFIGS = [
        LossBreakdownRowConfig(
            title="Incorrect Representment Marking",
            description="Chargebacks were accepted by NPCI/PG/Bank but were wrongly marked as \"Representment Completed\" on Stratos. This led to no recovery being initiated.",
            owner="Chargeback Ops",
            expected_actions="Chargeback Ops Team to review and correct the representment status on Stratos.",
            metric_key=MetricKeys.WRONGLY_MARKED_CHARGEBACK_AMOUNT.value
        ),
        LossBreakdownRowConfig(
            title="Discrepancy Between RGCS & NPCI Status",
            description="RGCS shows chargeback acceptance completed, but NPCI still accepted the chargeback and debited funds. This discrepancy led to unexpected loss.",
            owner="Chargeback Ops",
            expected_actions="Ops Team to investigate and provide clarification for such inconsistencies.",
            metric_key=MetricKeys.RGCS_ACCEPTED_AND_NPCI_ACCEPTED.value
        ),
        LossBreakdownRowConfig(
            title="Recovery Pending Post Debit Signal",
            description="Debit signal has been successfully processed, but recovery flow has not been triggered by Ops on Stratos.",
            owner="Chargeback Ops",
            expected_actions="Ops Team to initiate recovery for these accepted chargebacks.",
            metric_key=MetricKeys.DEBIT_SIGNAL_PROCESSED_PENDING_ON_OPS.value
        ),
        LossBreakdownRowConfig(
            title="Chargeback Stuck in Intermediate State",
            description="Chargebacks are still in an investigation or intermediate state and have not moved to the correct recovery path.",
            owner="Chargeback Ops",
            expected_actions="Ops Team to move these chargebacks to the appropriate state for closure/recovery.",
            metric_key=MetricKeys.PENDING_ON_OPS_TO_MOVE.value
        ),
        LossBreakdownRowConfig(
            title="Debit Signal Not Triggered on Stratos",
            description="Chargeback acceptance was correctly marked on Stratos, but debit signal was not processed due to system-level or process miss.",
            owner="Stratos",
            expected_actions="To ensure debit signal is processed for these cases.",
            metric_key=MetricKeys.DEBIT_SIGNAL_NOT_PROCESSED.value
        ),
        LossBreakdownRowConfig(
            title="Finance Absorption Pending",
            description="Ops team has requested finance to absorb the chargeback loss for certain cases. These are currently awaiting Finance team's decision.",
            owner="Finance",
            expected_actions="To evaluate and confirm the cases where chargeback loss is to be absorbed.",
            metric_key=MetricKeys.ABSORBED_REQUESTED_BY_OPS.value
        ),
        LossBreakdownRowConfig(
            title="Chargeback Absorbed by PhonePe",
            description="Chargeback accepted and correctly marked at Stratos. But the loss has been absorbed by Phonepe with finance approval",
            owner="Finance",
            expected_actions="Not an action item. Just for information & tracking",
            metric_key=MetricKeys.CHARGEBACK_ABSORBED.value
        ),
        LossBreakdownRowConfig(
            title="Money Recovery Reversed by Finance Team",
            description="Chargeback accepted and correctly marked at Stratos. The money has been also recovered. Then moved to reversal of recovery by the ops team with finance approval",
            owner="Finance",
            expected_actions="Not an action item. Just for information & tracking",
            metric_key=MetricKeys.MONEY_RECOVERY_REVERSED.value
        )
    ]

    @staticmethod
    def _create_empty_breakdown_row(headers) -> Dict[str, str]:
        return {header: "" for header in headers}

    @staticmethod
    def _populate_breakdown_metrics(row, headers, metrics) -> None:
        if metrics and len(headers) >= 5:
            metrics_dict = metrics.to_string_dict()
            row[headers[1]] = metrics_dict[ChannelKeys.UPI_CHARGEBACK.value]
            row[headers[2]] = metrics_dict[ChannelKeys.PG_CHARGEBACK.value]
            row[headers[3]] = metrics_dict[ChannelKeys.EDC_CHARGEBACK.value]
            row[headers[4]] = metrics_dict[ChannelKeys.TOTAL.value]

    @classmethod
    def generate_breakdown_row(cls, config, data_dictionary, headers) -> Dict[str, str]:
        row = cls._create_empty_breakdown_row(headers)

        if len(headers) >= 8:
            row[headers[0]] = config.title
            row[headers[5]] = config.description
            row[headers[6]] = config.owner
            row[headers[7]] = config.expected_actions

            metrics = data_dictionary.get(config.metric_key)
            cls._populate_breakdown_metrics(row, headers, metrics)

        return row

    @classmethod
    def generate_all_breakdown_rows(cls, data_dictionary, headers) -> List[Dict[str, str]]:
        return [
            cls.generate_breakdown_row(config, data_dictionary, headers)
            for config in cls.LOSS_BREAKDOWN_CONFIGS
        ]


def generate_loss_breakdown_with_ids(disputes: List[DisputeRecord]) -> List[Tuple[str, str, str, str]]:
    """Generate list of dispute_workflow_id, transaction_id with loss reasons"""
    loss_breakdown = []

    for dispute in disputes:
        dispute_type_str = get_dispute_type_string(dispute.dispute_type)
        amount_debited = _calculate_amount_debited(dispute)
        recovery_initiated = _calculate_recovery_initiated(dispute)
        loss = amount_debited - recovery_initiated

        if loss > 0:
            reason = _get_loss_reason(dispute)
            if reason:
                # Convert loss amount from paise to rupees
                loss_in_rupees = loss / 100
                loss_breakdown.append((
                    dispute.dispute_workflow_id,
                    dispute.transaction_id,
                    reason,
                    str(int(loss_in_rupees))
                ))

    return loss_breakdown


def _get_loss_reason(dispute: DisputeRecord) -> Optional[str]:
    """Determine the reason for loss based on dispute conditions"""
    # Incorrect Representment Marking
    if _calculate_wrongly_marked_chargeback(dispute) > 0:
        return "Incorrect Representment Marking"

    # Discrepancy Between RGCS & NPCI Status
    elif _calculate_rgcs_accepted_and_npci_accepted(dispute) > 0:
        return "Discrepancy Between RGCS & NPCI Status"

    # Recovery Pending Post Debit Signal
    elif _calculate_debit_signal_processed_pending_on_ops(dispute) > 0:
        return "Recovery Pending Post Debit Signal"

    # Chargeback Stuck in Intermediate State
    elif _calculate_pending_on_ops_to_move(dispute) > 0:
        return "Chargeback Stuck in Intermediate State"

    # Finance Absorption Pending
    elif _calculate_absorbed_requested_by_ops(dispute) > 0:
        return "Finance Absorption Pending"

    # Debit Signal Not Triggered on Stratos
    elif _calculate_debit_signal_not_processed(dispute) > 0:
        return "Debit Signal Not Triggered on Stratos"

    elif _calculate_money_recovery_reversed(dispute) > 0:
        return "Money Recovery Reversed by Finance Team"


def generate_chargeback_report_from_raw_data(input_csv_file) -> str:
    """Generate chargeback report from raw dispute data"""
    try:
        headers = ReportHeaders()
        main_headers = headers.MAIN_REPORT
        breakdown_headers = headers.BREAKDOWN_REPORT

        temp_csv_path = create_temporary_csv("chargeback_analysis_report")
        logging.info(f"Generating report at: {temp_csv_path}")

        # Parse raw dispute data and calculate metrics
        disputes = parse_raw_dispute_data(input_csv_file)
        data_dictionary = calculate_metrics_from_disputes(disputes)

        main_generator = MainReportRowGenerator()
        breakdown_generator = LossBreakdownReportRowGenerator()

        # Write main report headers first
        main_header_row = {header: header for header in main_headers}
        append_row_to_csv(temp_csv_path, main_header_row, main_headers)

        # Generate main report rows
        main_rows = [
            main_generator.get_total_chargeback_registered_row(data_dictionary, main_headers),
            main_generator.get_amount_debited_row(data_dictionary, main_headers),
            main_generator.get_merchant_recovery_initiated_row(data_dictionary, main_headers),
            main_generator.get_merchant_recovery_pending_row(data_dictionary, main_headers)
        ]

        for row in main_rows:
            append_row_to_csv(temp_csv_path, row, main_headers)

        write_empty_row(temp_csv_path, len(main_headers))
        write_empty_row(temp_csv_path, len(main_headers))

        # Generate breakdown report
        breakdown_header_row = {header: header for header in breakdown_headers}
        append_row_to_csv(temp_csv_path, breakdown_header_row, breakdown_headers)

        breakdown_rows = breakdown_generator.generate_all_breakdown_rows(data_dictionary, breakdown_headers)
        for row in breakdown_rows:
            append_row_to_csv(temp_csv_path, row, breakdown_headers)

        # Add loss breakdown with dispute IDs
        write_empty_row(temp_csv_path, len(breakdown_headers))
        write_empty_row(temp_csv_path, len(breakdown_headers))

        # Add detailed loss breakdown header
        loss_detail_headers = ["Dispute Workflow ID", "Transaction ID", "Loss Amount", "Loss Reason"]
        loss_detail_header_row = {header: header for header in loss_detail_headers}
        append_row_to_csv(temp_csv_path, loss_detail_header_row, loss_detail_headers)

        # Add loss breakdown data
        loss_breakdown = generate_loss_breakdown_with_ids(disputes)
        for workflow_id, transaction_id, reason, amount in loss_breakdown:
            loss_row = {
                "Dispute Workflow ID": workflow_id,
                "Transaction ID": transaction_id,
                "Loss Amount": amount,
                "Loss Reason": reason
            }
            append_row_to_csv(temp_csv_path, loss_row, loss_detail_headers)

        logging.info(f"Report generation completed: {temp_csv_path}")
        return temp_csv_path

    except Exception as e:
        error_msg = f"Failed to generate chargeback report from raw data {input_csv_file}: {e}"
        logging.error(error_msg)
        raise RuntimeError(error_msg)


def process(tsv_file_path, csv_file_path) -> None:
    """Process raw dispute data and generate chargeback financial impact report"""
    try:
        logging.info(f"Starting raw chargeback transformation: {tsv_file_path} -> {csv_file_path}")

        convert_tsv_to_csv(tsv_file_path, csv_file_path)
        temp_report_path = generate_chargeback_report_from_raw_data(csv_file_path)

        remove_file_pattern(csv_file_path)
        create_dir_for_file(csv_file_path)
        create_empty_csv(csv_file_path)

        report_content = read_file_content(temp_report_path)
        append_to_file_top_safely(csv_file_path, report_content)

        logging.info(f"Raw chargeback transformation completed successfully: {csv_file_path}")

    except Exception as e:
        error_msg = f"Failed to process raw chargeback transformation: {e}"
        logging.error(error_msg)
        raise ValueError(error_msg)
