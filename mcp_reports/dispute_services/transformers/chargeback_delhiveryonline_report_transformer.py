import csv
import logging
import pandas as pd
from mcp_reports.dispute_services.config.config import ROSEY_CONFIG
from mcp_reports.dispute_services.utils.stratos_client import StratosClient

def get_status(state_number):
    state_mapper = ROSEY_CONFIG.get('stratos_state_mapper')
    reversed_state_mapper = {
    num: status
    for status, state_numbers in state_mapper.items()
    for num in state_numbers
    }
    res = reversed_state_mapper.get(state_number, 'Unknown Status')
    return res

def process(tsv_file_path, csv_file_path):

    global response_map

    stratos_client = StratosClient()

    try:
        res = stratos_client.get_enums("")
        response_map = res.json()
    except Exception as e:
        logging.error("Failed to get enum values: {}".format(e))
        raise e

    logging.info("Successfully get the enum values: {}".format(response_map))

    df = pd.read_csv(tsv_file_path, sep='\t', header=None)
    
    df[8] = df[5].apply(get_status)
    df[2] = df[2].apply(
        lambda x: response_map.get('DISPUTE_STAGE', {}).get(str(x), x)
    )
    df[5] = df[5].apply(
        lambda x: response_map.get('DISPUTE_WORK_FLOW_STATE', {}).get(str(x), x)
    )
    df.to_csv(csv_file_path, index=False, header=False)
    
    logging.info(f"Successfully processed data and saved to {csv_file_path}")



