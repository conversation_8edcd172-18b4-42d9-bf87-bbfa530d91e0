import yaml
import logging

CACHE = {}


def get_rosey_config(env, rosey_config_path, team_id, project_id):
    if is_stage_env(env) or is_prod_env(env):
        from configprovider import ConfigProvider
        config_provider = ConfigProvider.get_config_provider(rosey_config_path)
        config_as_string = config_provider.get_project_config_v2(team_id, project_id)
        if config_as_string.status_code != 200:
            raise Exception('Failed to fetch rosey config')
        else:
            return yaml.safe_load(config_as_string.content)
    else:
        return yaml.safe_load(open(rosey_config_path, 'r'))

def get_rosey_config_cached(env, rosey_config_path, team_id, project_id):

  logging.info("fetching rosey config for team_id ({}) and project_id ({})".format(team_id, project_id))
  cache_key = "{}__{}".format(team_id, project_id)

  if CACHE.get(cache_key):
    return CACHE.get(cache_key)

  else:
    CACHE[cache_key] = get_rosey_config(env, rosey_config_path, team_id, project_id)
    return CACHE[cache_key]


def is_stage_env(env):
    return env.lower() == 'stage'


def is_prod_env(env):
    return env.lower() == 'prod'
