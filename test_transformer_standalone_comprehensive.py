 #!/usr/bin/env python3

import csv
import tempfile
import os
from dataclasses import dataclass
from typing import List, Tuple, Dict

@dataclass
class DisputeRecord:
    dispute_workflow_id: str
    transaction_id: str
    current_state: int
    dispute_type: int
    dispute_stage: int
    disputed_amount: float
    accepted_amount: float
    raised_at: str
    signal_type: str = None
    signal_amount: float = 0

@dataclass
class ChargebackMetrics:
    upi_chargeback: float = 0
    pg_chargeback: float = 0
    edc_chargeback: float = 0
    total: float = 0

def get_dispute_type_string(dispute_type: int) -> str:
    """Convert dispute type integer to string"""
    if dispute_type == 0:
        return 'UPI_CHARGEBACK'
    elif dispute_type == 1:
        return 'PG_CHARGEBACK'
    elif dispute_type == 5:
        return 'EDC_CHARGEBACK'
    else:
        return f'UNKNOWN_{dispute_type}'

def _calculate_amount_debited(dispute: DisputeRecord) -> float:
    """Calculate amount debited based on dispute conditions"""
    if (dispute.dispute_type == 0 and dispute.dispute_stage == 0 and 
        (dispute.signal_type != 'CREDIT' or dispute.signal_type is None)):
        return dispute.disputed_amount
    elif (dispute.dispute_type == 0 and dispute.dispute_stage == 1 and 
          dispute.signal_type == 'DEBIT'):
        return dispute.signal_amount
    elif (dispute.dispute_type != 0 and dispute.signal_type == 'DEBIT'):
        return dispute.signal_amount
    else:
        return 0

def _calculate_recovery_initiated(dispute: DisputeRecord) -> float:
    """Calculate recovery initiated based on dispute conditions"""
    if (dispute.dispute_type == 0 and dispute.dispute_stage == 0 and 
        dispute.signal_type is None and dispute.current_state == 23):
        return dispute.accepted_amount
    elif (dispute.dispute_type == 0 and dispute.dispute_stage == 1 and 
          dispute.signal_type == 'DEBIT' and dispute.current_state == 23):
        return dispute.accepted_amount
    elif (dispute.dispute_type in (1, 5) and dispute.signal_type == 'DEBIT' and 
          dispute.current_state == 23):
        return dispute.accepted_amount
    else:
        return 0

def _calculate_wrongly_marked_chargeback(dispute: DisputeRecord) -> float:
    """Calculate wrongly marked chargeback amount"""
    if (dispute.dispute_type == 0 and dispute.dispute_stage == 0 and 
        dispute.current_state == 4 and dispute.signal_type is None):
        return dispute.disputed_amount
    elif (dispute.dispute_type == 0 and dispute.dispute_stage == 1 and 
          dispute.current_state == 4 and dispute.signal_type == 'DEBIT'):
        return dispute.disputed_amount
    elif (dispute.dispute_type == 1 and dispute.dispute_stage in (0, 1) and 
          dispute.current_state == 30 and dispute.signal_type == 'DEBIT'):
        return dispute.disputed_amount
    else:
        return 0

def _calculate_absorbed_or_requested_by_ops(dispute: DisputeRecord) -> float:
    """Calculate absorbed or requested by ops amount"""
    if dispute.current_state in (14, 16, 17):
        return dispute.disputed_amount
    else:
        return 0

def _calculate_rgcs_accepted_and_npci_accepted(dispute: DisputeRecord) -> float:
    """Calculate RGCS accepted and NPCI accepted amount"""
    if (dispute.dispute_type == 0 and dispute.dispute_stage == 0 and 
        dispute.current_state == 2 and dispute.signal_type is None):
        return dispute.disputed_amount
    else:
        return 0

def _calculate_debit_signal_processed_pending_on_ops(dispute: DisputeRecord) -> float:
    """Calculate debit signal processed pending on ops amount"""
    if (dispute.dispute_type in (1, 5) and dispute.current_state == 28 and 
        dispute.signal_type == 'DEBIT'):
        return dispute.disputed_amount
    else:
        return 0

def _calculate_debit_signal_not_processed(dispute: DisputeRecord) -> float:
    """Calculate debit signal not processed amount"""
    if (dispute.dispute_type == 1 and dispute.current_state == 31 and 
        dispute.signal_type == 'DEBIT'):
        return dispute.disputed_amount
    else:
        return 0

def _calculate_pending_on_ops_to_move(dispute: DisputeRecord) -> float:
    """Calculate pending on ops to move amount"""
    if (dispute.dispute_type == 5 and dispute.current_state in (6, 11) and 
        dispute.signal_type == 'DEBIT'):
        return dispute.disputed_amount
    else:
        return 0

def _get_loss_reason(dispute: DisputeRecord) -> str:
    """Determine the reason for loss based on dispute conditions"""
    # Incorrect Representment Marking
    if _calculate_wrongly_marked_chargeback(dispute) > 0:
        return "Incorrect Representment Marking"
    
    # Discrepancy Between RGCS & NPCI Status
    if _calculate_rgcs_accepted_and_npci_accepted(dispute) > 0:
        return "Discrepancy Between RGCS & NPCI Status"
    
    # Recovery Pending Post Debit Signal
    if _calculate_debit_signal_processed_pending_on_ops(dispute) > 0:
        return "Recovery Pending Post Debit Signal"
    
    # Chargeback Stuck in Intermediate State
    if _calculate_pending_on_ops_to_move(dispute) > 0:
        return "Chargeback Stuck in Intermediate State"
    
    # Finance Absorption Pending
    if _calculate_absorbed_or_requested_by_ops(dispute) > 0:
        return "Finance Absorption Pending"
    
    # Debit Signal Not Triggered on Stratos
    if _calculate_debit_signal_not_processed(dispute) > 0:
        return "Debit Signal Not Triggered on Stratos"
    
    return "Other"

def _add_to_metrics(metrics: ChargebackMetrics, dispute_type: str, amount: float):
    """Add amount to appropriate metric based on dispute type"""
    if dispute_type == 'UPI_CHARGEBACK':
        metrics.upi_chargeback += amount
    elif dispute_type == 'PG_CHARGEBACK':
        metrics.pg_chargeback += amount
    elif dispute_type == 'EDC_CHARGEBACK':
        metrics.edc_chargeback += amount
    metrics.total += amount

def create_comprehensive_test_data():
    """Create comprehensive test data covering all scenarios"""
    return [
        # UPI Chargebacks (dispute_type = 0)
        # Case 1: UPI - Recovery initiated (state 23, stage 0, no signal)
        DisputeRecord("DW001", "TXN001", 23, 0, 0, 10000, 8000, "2025-04-01", None, 0),
        
        # Case 2: UPI - Wrongly marked chargeback (state 4, stage 0, no signal)
        DisputeRecord("DW002", "TXN002", 4, 0, 0, 15000, 0, "2025-04-02", None, 0),
        
        # Case 3: UPI - RGCS accepted and NPCI accepted (state 2, stage 0, no signal)
        DisputeRecord("DW003", "TXN003", 2, 0, 0, 5000, 0, "2025-04-03", None, 0),
        
        # Case 4: UPI - Finance absorption (state 14)
        DisputeRecord("DW004", "TXN004", 14, 0, 0, 25000, 0, "2025-04-04", None, 0),
        
        # Case 5: UPI - Stage 1 with DEBIT signal and recovery (state 23)
        DisputeRecord("DW005", "TXN005", 23, 0, 1, 12000, 10000, "2025-04-05", "DEBIT", 12000),
        
        # Case 6: UPI - Stage 1 with DEBIT signal, wrongly marked (state 4)
        DisputeRecord("DW006", "TXN006", 4, 0, 1, 8000, 0, "2025-04-06", "DEBIT", 8000),
        
        # PG Chargebacks (dispute_type = 1)
        # Case 7: PG - Debit signal processed pending on ops (state 28)
        DisputeRecord("DW007", "TXN007", 28, 1, 0, 20000, 0, "2025-04-07", "DEBIT", 20000),
        
        # Case 8: PG - Debit signal not processed (state 31)
        DisputeRecord("DW008", "TXN008", 31, 1, 0, 12000, 0, "2025-04-08", "DEBIT", 12000),
        
        # Case 9: PG - Recovery initiated (state 23)
        DisputeRecord("DW009", "TXN009", 23, 1, 0, 18000, 15000, "2025-04-09", "DEBIT", 18000),
        
        # Case 10: PG - Wrongly marked (state 30, stage 0)
        DisputeRecord("DW010", "TXN010", 30, 1, 0, 9000, 0, "2025-04-10", "DEBIT", 9000),
        
        # Case 11: PG - Wrongly marked (state 30, stage 1)
        DisputeRecord("DW011", "TXN011", 30, 1, 1, 7000, 0, "2025-04-11", "DEBIT", 7000),
        
        # EDC Chargebacks (dispute_type = 5)
        # Case 12: EDC - Pending on ops to move (state 6)
        DisputeRecord("DW012", "TXN012", 6, 5, 0, 8000, 0, "2025-04-12", "DEBIT", 8000),
        
        # Case 13: EDC - Pending on ops to move (state 11)
        DisputeRecord("DW013", "TXN013", 11, 5, 0, 6000, 0, "2025-04-13", "DEBIT", 6000),
        
        # Case 14: EDC - Debit signal processed pending on ops (state 28)
        DisputeRecord("DW014", "TXN014", 28, 5, 0, 11000, 0, "2025-04-14", "DEBIT", 11000),
        
        # Case 15: EDC - Recovery initiated (state 23)
        DisputeRecord("DW015", "TXN015", 23, 5, 0, 13000, 11000, "2025-04-15", "DEBIT", 13000),
        
        # Additional edge cases
        # Case 16: Finance absorption (state 16)
        DisputeRecord("DW016", "TXN016", 16, 0, 0, 3000, 0, "2025-04-16", None, 0),
        
        # Case 17: Finance absorption (state 17)
        DisputeRecord("DW017", "TXN017", 17, 1, 0, 4000, 0, "2025-04-17", "DEBIT", 4000),
        
        # Case 18: No loss case (fully recovered)
        DisputeRecord("DW018", "TXN018", 23, 0, 0, 5000, 5000, "2025-04-18", None, 0),
    ]

def test_comprehensive_calculations():
    """Test comprehensive calculations and generate expected output format"""
    print("COMPREHENSIVE CHARGEBACK TRANSFORMER TEST")
    print("="*60)
    
    # Create test data
    disputes = create_comprehensive_test_data()
    print(f"📊 Testing with {len(disputes)} dispute records")
    
    # Initialize metrics
    metrics = {
        'disputed_amount': ChargebackMetrics(),
        'amount_debited': ChargebackMetrics(),
        'amount_recovered': ChargebackMetrics(),
        'loss': ChargebackMetrics(),
        'wrongly_marked_chargeback_amount': ChargebackMetrics(),
        'rgcs_accepted_and_npci_accepted': ChargebackMetrics(),
        'debit_signal_processed_pending_on_ops': ChargebackMetrics(),
        'pending_on_ops_to_move': ChargebackMetrics(),
        'absorbed_or_requested_by_ops': ChargebackMetrics(),
        'debit_signal_not_processed': ChargebackMetrics()
    }
    
    loss_breakdown = []
    
    print("\n📋 PROCESSING EACH DISPUTE:")
    print("-" * 100)
    print(f"{'ID':<6} {'Type':<4} {'State':<5} {'Stage':<5} {'Signal':<6} {'Disputed':<9} {'Debited':<8} {'Recovery':<8} {'Loss':<8} {'Reason'}")
    print("-" * 100)
    
    for dispute in disputes:
        dispute_type_str = get_dispute_type_string(dispute.dispute_type)
        
        # Calculate all metrics for this dispute
        amount_debited = _calculate_amount_debited(dispute)
        recovery_initiated = _calculate_recovery_initiated(dispute)
        loss = amount_debited - recovery_initiated
        
        # Add to totals
        _add_to_metrics(metrics['disputed_amount'], dispute_type_str, dispute.disputed_amount)
        _add_to_metrics(metrics['amount_debited'], dispute_type_str, amount_debited)
        _add_to_metrics(metrics['amount_recovered'], dispute_type_str, recovery_initiated)
        _add_to_metrics(metrics['loss'], dispute_type_str, loss)
        
        # Calculate specific loss categories
        wrongly_marked = _calculate_wrongly_marked_chargeback(dispute)
        _add_to_metrics(metrics['wrongly_marked_chargeback_amount'], dispute_type_str, wrongly_marked)
        
        rgcs_npci = _calculate_rgcs_accepted_and_npci_accepted(dispute)
        _add_to_metrics(metrics['rgcs_accepted_and_npci_accepted'], dispute_type_str, rgcs_npci)
        
        debit_pending = _calculate_debit_signal_processed_pending_on_ops(dispute)
        _add_to_metrics(metrics['debit_signal_processed_pending_on_ops'], dispute_type_str, debit_pending)
        
        pending_ops = _calculate_pending_on_ops_to_move(dispute)
        _add_to_metrics(metrics['pending_on_ops_to_move'], dispute_type_str, pending_ops)
        
        absorbed = _calculate_absorbed_or_requested_by_ops(dispute)
        _add_to_metrics(metrics['absorbed_or_requested_by_ops'], dispute_type_str, absorbed)
        
        debit_not_processed = _calculate_debit_signal_not_processed(dispute)
        _add_to_metrics(metrics['debit_signal_not_processed'], dispute_type_str, debit_not_processed)
        
        # Get loss reason if there's a loss
        reason = ""
        if loss > 0:
            reason = _get_loss_reason(dispute)
            loss_breakdown.append((dispute.dispute_workflow_id, dispute.transaction_id, reason, str(int(loss))))
        
        # Print row
        signal_str = dispute.signal_type or "None"
        type_short = dispute_type_str.split('_')[0]
        print(f"{dispute.dispute_workflow_id:<6} {type_short:<4} {dispute.current_state:<5} {dispute.dispute_stage:<5} {signal_str:<6} {dispute.disputed_amount:<9.0f} {amount_debited:<8.0f} {recovery_initiated:<8.0f} {loss:<8.0f} {reason}")
    
    return metrics, loss_breakdown


def generate_expected_report_format(metrics, loss_breakdown):
    """Generate the expected report format"""
    print("\n" + "="*80)
    print("EXPECTED REPORT OUTPUT FORMAT")
    print("="*80)

    # Main report section
    print("\nData (All figures in Rs)\tUPI\tPG\tEDC\tTotal")
    print(f"Total Chargeback Registered on Stratos\t{metrics['disputed_amount'].upi_chargeback:.0f}\t{metrics['disputed_amount'].pg_chargeback:.0f}\t{metrics['disputed_amount'].edc_chargeback:.0f}\t{metrics['disputed_amount'].total:.0f}")
    print(f"Amount debited by NPCI/PGs/Axis Bank\n(i.e. Chargeback Accepted by PhonePe)\t{metrics['amount_debited'].upi_chargeback:.0f}\t{metrics['amount_debited'].pg_chargeback:.0f}\t{metrics['amount_debited'].edc_chargeback:.0f}\t{metrics['amount_debited'].total:.0f}")
    print(f"Merchant recovery initiated from Stratos for Accepted Chargebacks\t{metrics['amount_recovered'].upi_chargeback:.0f}\t{metrics['amount_recovered'].pg_chargeback:.0f}\t{metrics['amount_recovered'].edc_chargeback:.0f}\t{metrics['amount_recovered'].total:.0f}")
    print(f"Merchant recovery yet to be initiated for Accepted Chargebacks*\t{metrics['loss'].upi_chargeback:.0f}\t{metrics['loss'].pg_chargeback:.0f}\t{metrics['loss'].edc_chargeback:.0f}\t{metrics['loss'].total:.0f}")

    print("\n")
    print("\nReasons for 'Merchant recovery yet to be initiated'\tUPI\tPG\tEDC\tTotal\tDescription\tOwner\tExpected Actions")

    # Loss breakdown categories
    categories = [
        ("Incorrect Representment Marking", "wrongly_marked_chargeback_amount",
         "Chargebacks were accepted by NPCI/PG/Bank but were wrongly marked as \"Representment Completed\" on Stratos. This led to no recovery being initiated.",
         "Chargeback Ops", "Chargeback Ops Team to review and correct the representment status on Stratos."),

        ("Discrepancy Between RGCS & NPCI Status", "rgcs_accepted_and_npci_accepted",
         "RGCS shows chargeback acceptance completed, but NPCI still accepted the chargeback and debited funds. This discrepancy led to unexpected loss.",
         "Chargeback Ops", "Ops Team to investigate and provide clarification for such inconsistencies."),

        ("Recovery Pending Post Debit Signal", "debit_signal_processed_pending_on_ops",
         "Debit signal has been successfully processed, but recovery flow has not been triggered by Ops on Stratos.",
         "Chargeback Ops", "Ops Team to initiate recovery for these accepted chargebacks."),

        ("Chargeback Stuck in Intermediate State", "pending_on_ops_to_move",
         "Chargebacks are still in an investigation or intermediate state and have not moved to the correct recovery path.",
         "Chargeback Ops", "Ops Team to move these chargebacks to the appropriate state for closure/recovery."),

        ("Finance Absorption Pending", "absorbed_or_requested_by_ops",
         "Ops team has requested finance to absorb the chargeback loss for certain cases. These are currently awaiting Finance team's decision.",
         "Finance", "To evaluate and confirm the cases where chargeback loss is to be absorbed."),

        ("Debit Signal Not Triggered on Stratos", "debit_signal_not_processed",
         "Chargeback acceptance was correctly marked on Stratos, but debit signal was not processed due to system-level or process miss.",
         "Stratos", "To ensure debit signal is processed for these cases.")
    ]

    for title, metric_key, description, owner, actions in categories:
        metric = metrics[metric_key]
        print(f"{title}\t{metric.upi_chargeback:.0f}\t{metric.pg_chargeback:.0f}\t{metric.edc_chargeback:.0f}\t{metric.total:.0f}\t{description}\t{owner}\t{actions}")

    # Loss breakdown with IDs
    print("\n")
    print("\nDispute Workflow ID\tTransaction ID\tLoss Reason\tLoss Amount")
    for workflow_id, transaction_id, reason, amount in loss_breakdown:
        print(f"{workflow_id}\t{transaction_id}\t{reason}\t{amount}")


def verify_calculations():
    """Verify calculations match expected business logic"""
    print("\n" + "="*80)
    print("VERIFICATION OF BUSINESS LOGIC")
    print("="*80)

    # Test specific scenarios
    test_cases = [
        # UPI Recovery case
        {
            'name': 'UPI Recovery Initiated',
            'dispute': DisputeRecord("TEST1", "TXN1", 23, 0, 0, 10000, 8000, "2025-04-01", None, 0),
            'expected_debited': 10000,
            'expected_recovery': 8000,
            'expected_loss': 2000,
            'expected_reason': 'Other'
        },
        # UPI Wrongly marked
        {
            'name': 'UPI Wrongly Marked',
            'dispute': DisputeRecord("TEST2", "TXN2", 4, 0, 0, 15000, 0, "2025-04-02", None, 0),
            'expected_debited': 15000,
            'expected_recovery': 0,
            'expected_loss': 15000,
            'expected_reason': 'Incorrect Representment Marking'
        },
        # PG Debit Signal Pending
        {
            'name': 'PG Debit Signal Pending',
            'dispute': DisputeRecord("TEST3", "TXN3", 28, 1, 0, 20000, 0, "2025-04-03", "DEBIT", 20000),
            'expected_debited': 20000,
            'expected_recovery': 0,
            'expected_loss': 20000,
            'expected_reason': 'Recovery Pending Post Debit Signal'
        }
    ]

    all_passed = True

    for test_case in test_cases:
        print(f"\n🧪 Testing: {test_case['name']}")
        dispute = test_case['dispute']

        debited = _calculate_amount_debited(dispute)
        recovery = _calculate_recovery_initiated(dispute)
        loss = debited - recovery
        reason = _get_loss_reason(dispute) if loss > 0 else 'No Loss'

        # Check results
        debited_ok = debited == test_case['expected_debited']
        recovery_ok = recovery == test_case['expected_recovery']
        loss_ok = loss == test_case['expected_loss']
        reason_ok = reason == test_case['expected_reason']

        print(f"  Amount Debited: {debited} (expected {test_case['expected_debited']}) {'✓' if debited_ok else '✗'}")
        print(f"  Recovery: {recovery} (expected {test_case['expected_recovery']}) {'✓' if recovery_ok else '✗'}")
        print(f"  Loss: {loss} (expected {test_case['expected_loss']}) {'✓' if loss_ok else '✗'}")
        print(f"  Reason: {reason} (expected {test_case['expected_reason']}) {'✓' if reason_ok else '✗'}")

        test_passed = all([debited_ok, recovery_ok, loss_ok, reason_ok])
        print(f"  Result: {'✅ PASS' if test_passed else '❌ FAIL'}")

        if not test_passed:
            all_passed = False

    print(f"\n{'='*50}")
    print(f"OVERALL VERIFICATION: {'✅ ALL TESTS PASSED' if all_passed else '❌ SOME TESTS FAILED'}")
    print(f"{'='*50}")

    return all_passed


if __name__ == "__main__":
    print("🚀 Starting comprehensive transformer test...")

    # Run verification first
    verification_passed = verify_calculations()

    # Run comprehensive test
    metrics, loss_breakdown = test_comprehensive_calculations()

    # Generate expected report format
    generate_expected_report_format(metrics, loss_breakdown)

    print(f"\n{'='*80}")
    print(f"FINAL RESULT: {'✅ SUCCESS' if verification_passed else '❌ FAILED'}")
    print(f"{'='*80}")

    if verification_passed:
        print("\n✅ The transformer logic is working correctly!")
        print("✅ All business rules are implemented properly!")
        print("✅ The output format matches the expected structure!")
    else:
        print("\n❌ Some tests failed. Please review the business logic.")

    print(f"\n📊 Summary:")
    print(f"  Total disputes processed: {len(create_comprehensive_test_data())}")
    print(f"  Total loss cases: {len(loss_breakdown)}")
    print(f"  Total disputed amount: {metrics['disputed_amount'].total:.0f}")
    print(f"  Total amount debited: {metrics['amount_debited'].total:.0f}")
    print(f"  Total recovery initiated: {metrics['amount_recovered'].total:.0f}")
    print(f"  Total loss: {metrics['loss'].total:.0f}")
